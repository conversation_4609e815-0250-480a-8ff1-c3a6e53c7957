# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Configuration for excavation environment with enhanced grid perception."""

from __future__ import annotations

import math
from dataclasses import MISSING

from isaaclab.utils import configclass

import moleworks_ext.tasks.excavation.mdp as mdp
from moleworks_ext.common.actions.actions_cfg import InverseDynamicsActionCfg
from moleworks_ext.common.managers.observations.obs_with_mean import ObservationWithMeanTermCfg as ObsTerm
from moleworks_ext.tasks.excavation.env_cfg.excavation_env_cfg import ExcavationEnvCfg
from moleworks_ext.tasks.excavation.env_cfg.m545_env_cfg import ExcavationSceneCfg

from isaaclab.envs import ManagerBasedRLEnvCfg
from isaaclab.managers import CurriculumTermCfg as CurrTerm
from isaaclab.managers import EventTermCfg as EventTerm
from isaaclab.managers import ObservationGroupCfg as ObsGroup
from isaaclab.managers import ObservationTermCfg as ObsTerm
from isaaclab.managers import RewardTermCfg as RewTerm
from isaaclab.managers import SceneEntityCfg
from isaaclab.managers import TerminationTermCfg as DoneTerm
from isaaclab.utils import configclass
from isaaclab.utils.noise import AdditiveUniformNoiseCfg as Unoise


##
# Pre-defined configs
##
from moleworks_ext.tasks.excavation.env_cfg.m545_env_cfg import M545EnvCfg


@configclass
class ActionsCfg:
    """Action specifications for the MDP."""

    # Use inverse dynamics action
    arm_action = InverseDynamicsActionCfg(
        asset_name="robot",
        joint_names=["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"],
        scale=0.5,
        use_default_offset=True,
    )


@configclass
class ObservationsCfg:
    """Observation specifications for the MDP."""

    @configclass
    class PolicyCfg(ObsGroup):
        """Observations for policy group with enhanced grid perception."""

        # Robot State Observations (16 dimensions total)
        # Joint positions (4 dimensions)
        dof_pos = ObsTerm(func=mdp.dof_pos, params={"asset_cfg": SceneEntityCfg("robot", joint_names=[".*"])})
        
        # Joint velocities (4 dimensions)
        dof_vel = ObsTerm(
            func=mdp.dof_vel,
            params={"asset_cfg": SceneEntityCfg("robot", joint_names=[".*"])},
            scale=(1 / 0.1, 1 / 0.1, 1 / 0.2, 1 / 0.2),
        )
        
        # Joint torques (4 dimensions)
        dof_tau = ObsTerm(
            func=mdp.dof_tau,
            mean=[-2.66e4, 2.45e4, 4.24e4, 1.19e4],
            scale=(1 / 1.6e5, 1 / 1.44e5, 1 / 1.48e5, 1 / 5.47e4),
        )
        
        # Last action (4 dimensions)
        last_action_excavation = ObsTerm(func=mdp.last_action_excavation)

        # Bucket State Observations (8 dimensions total)
        # Bucket linear position and velocity in base frame (4 dimensions)
        bucket_lin_gac = ObsTerm(func=mdp.bucket_lin_gac_base)
        
        # Bucket angular position and velocity (2 dimensions)
        bucket_ang_gac = ObsTerm(func=mdp.bucket_ang_gac)
        
        # Bucket velocity magnitude (1 dimension)
        bucket_lin_vel_norm = ObsTerm(func=mdp.bucket_lin_vel_norm, mean=[0.25], scale=(1 / 0.25))
        
        # Bucket angle of attack (1 dimension)
        bucket_aoa = ObsTerm(func=mdp.bucket_angle_of_attack_obs)

        # Enhanced Spatial Perception (25 dimensions)
        # 5x5 grid: 5 points across bucket width × 5 points ahead, 30cm spacing
        spatial_perception = ObsTerm(
            func=mdp.bucket_spatial_perception,
            mean=[-1.0] * 25,  # Expected soil height relative to base
            scale=[1.0 / 2.0] * 25,  # Normalize by typical height range
        )

        # Task-Specific Observations (7 dimensions total)
        # Bucket fill ratio (1 dimension)
        fill_ratio = ObsTerm(func=mdp.fill_ratio)
        
        # Traditional soil height futures for comparison (5 dimensions)
        soil_height = ObsTerm(
            func=mdp.soil_height,
            mean=[-1.25, -1.25, -1.25, -1.25, -1.25],
            scale=[1.75, 1.75, 1.75, 1.75, 1.75],
        )
        
        # Pullup distance (1 dimension)
        pullup_dist = ObsTerm(func=mdp.pullup_dist, mean=[2.75], scale=[0.75])

        def __post_init__(self):
            self.enable_corruption = True
            self.concatenate_terms = True

    # observation groups
    policy: PolicyCfg = PolicyCfg()


@configclass
class M545ExcavationSpatialPerceptionCfg(M545EnvCfg):
    """Configuration for excavation environment with practical spatial perception.

    Total observation dimensions: 16 + 8 + 25 + 7 = 56 dimensions

    Breakdown:
    - Robot state: 16 dims (4 pos + 4 vel + 4 torque + 4 last_action)
    - Bucket state: 8 dims (4 lin_gac + 2 ang_gac + 1 vel_norm + 1 aoa)
    - Spatial perception: 25 dims (5×5 grid: 5 across bucket width × 5 ahead)
    - Task-specific: 7 dims (1 fill_ratio + 5 soil_height + 1 pullup_dist)

    Spatial perception covers:
    - Width: 1.2m across bucket (5 points × 30cm spacing)
    - Forward: 1.2m ahead of bucket (5 points × 30cm spacing)
    - Total coverage: 1.2m × 1.2m area around bucket tip
    """

    # Override observations to use enhanced spatial perception
    observations: ObservationsCfg = ObservationsCfg()
    actions: ActionsCfg = ActionsCfg()

    def __post_init__(self):
        """Post initialization."""
        # Update soil model configuration for better 3D support
        self.soil_model_cfg.type = "3d"
        self.soil_model_cfg.model_3d.num_rays_x = 5
        self.soil_model_cfg.model_3d.num_rays_y = 5
        self.soil_model_cfg.model_3d.cell_size = 0.15  # Finer resolution for better perception


@configclass
class M545ExcavationForwardPerceptionCfg(M545EnvCfg):
    """Configuration for excavation environment with minimal forward perception upgrade.

    Total observation dimensions: 16 + 8 + 5 + 7 = 36 dimensions

    Breakdown:
    - Robot state: 16 dims (4 pos + 4 vel + 4 torque + 4 last_action)
    - Bucket state: 8 dims (4 lin_gac + 2 ang_gac + 1 vel_norm + 1 aoa)
    - Forward perception: 5 dims (5 points ahead of bucket, 30cm spacing)
    - Task-specific: 7 dims (1 fill_ratio + 5 soil_height + 1 pullup_dist)

    This is a minimal upgrade from current 1D perception:
    - Same 5 forward points as existing soil_height observation
    - But bucket-relative instead of fixed spacing
    - Compatible with both 2D and 3D soil models
    """

    @configclass
    class ForwardObservationsCfg:
        """Forward-only observation specifications for the MDP."""

        @configclass
        class PolicyCfg(ObsGroup):
            """Observations for policy group with forward perception."""

            # Robot State Observations (16 dimensions total)
            dof_pos = ObsTerm(func=mdp.dof_pos, params={"asset_cfg": SceneEntityCfg("robot", joint_names=[".*"])})
            dof_vel = ObsTerm(
                func=mdp.dof_vel,
                params={"asset_cfg": SceneEntityCfg("robot", joint_names=[".*"])},
                scale=(1 / 0.1, 1 / 0.1, 1 / 0.2, 1 / 0.2),
            )
            dof_tau = ObsTerm(
                func=mdp.dof_tau,
                mean=[-2.66e4, 2.45e4, 4.24e4, 1.19e4],
                scale=(1 / 1.6e5, 1 / 1.44e5, 1 / 1.48e5, 1 / 5.47e4),
            )
            last_action_excavation = ObsTerm(func=mdp.last_action_excavation)

            # Bucket State Observations (8 dimensions total)
            bucket_lin_gac = ObsTerm(func=mdp.bucket_lin_gac_base)
            bucket_ang_gac = ObsTerm(func=mdp.bucket_ang_gac)
            bucket_lin_vel_norm = ObsTerm(func=mdp.bucket_lin_vel_norm, mean=[0.25], scale=(1 / 0.25))
            bucket_aoa = ObsTerm(func=mdp.bucket_angle_of_attack_obs)

            # Forward Perception (5 dimensions)
            # 5 points ahead of bucket with 30cm spacing
            forward_perception = ObsTerm(
                func=mdp.bucket_forward_perception,
                mean=[-1.0] * 5,  # Expected soil height relative to base
                scale=[1.0 / 2.0] * 5,  # Normalize by typical height range
            )

            # Task-Specific Observations (7 dimensions total)
            fill_ratio = ObsTerm(func=mdp.fill_ratio)
            soil_height = ObsTerm(
                func=mdp.soil_height,
                mean=[-1.25, -1.25, -1.25, -1.25, -1.25],
                scale=[1.75, 1.75, 1.75, 1.75, 1.75],
            )
            pullup_dist = ObsTerm(func=mdp.pullup_dist, mean=[2.75], scale=[0.75])

            def __post_init__(self):
                self.enable_corruption = True
                self.concatenate_terms = True

        # observation groups
        policy: PolicyCfg = PolicyCfg()

    # Override observations to use forward perception
    observations: ForwardObservationsCfg = ForwardObservationsCfg()
    actions: ActionsCfg = ActionsCfg()

    def __post_init__(self):
        """Post initialization."""
        # Works with both 2D and 3D soil models
        self.soil_model_cfg.type = "3d"  # Can also use "1d" for backward compatibility
