# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
import torch
from typing import TYPE_CHECKING

from isaaclab.assets import Articulation, RigidObject
from isaaclab.envs import ManagerBasedEnv
from isaaclab.managers import SceneEntityCfg
import isaacsim.core.utils.torch as torch_utils


def dof_pos(env: ManagerBasedEnv, asset_cfg: SceneEntityCfg = SceneEntityCfg("robot")) -> torch.Tensor:
    result = env.m545_measurements.arm_joint_pos
    # print("dof_pos:", result)
    return result


def dof_vel(env: ManagerBasedEnv, asset_cfg: SceneEntityCfg = SceneEntityCfg("robot")) -> torch.Tensor:
    result = env.m545_measurements.arm_joint_vel
    # print("dof_vel:", result)
    return result


def dof_tau(env) -> torch.Tensor:
    result = env.m545_measurements.arm_joint_tau
    # print("dof_tau:", result)
    return result


def last_action_excavation(env) -> torch.Tensor:
    result = env.actions
    # print("last_action_excavation:", result)
    return result


def bucket_lin_gac(env) -> torch.Tensor:
    result = torch.cat(
        (env.m545_measurements.bucket_pos_base[:, [0, 2]], env.m545_measurements.bucket_vel_base[:, [0, 2]]), dim=-1
    )
    # print("bucket_lin_gac:", result)
    return result


def bucket_lin_gac_base(env) -> torch.Tensor:
    return torch.cat(
        (env.m545_measurements.bucket_pos_base[:, [0, 2]], env.m545_measurements.bucket_vel_base[:, [0, 2]]), dim=-1
    )

def bucket_ang_gac(env) -> torch.Tensor:
    result = torch.cat(
        (env.m545_measurements.bucket_ang_gac.unsqueeze(-1), env.m545_measurements.bucket_ang_vel_base[:, 1:2]), dim=-1
    )
    # print("bucket_ang_gac:", result)
    return result


def bucket_lin_vel_norm(env) -> torch.Tensor:
    result = env.m545_measurements.bucket_vel_norm.unsqueeze(-1)
    # print("bucket_lin_vel_norm:", result)
    return result


def base_pitch_gac(env) -> torch.Tensor:
    result = env.m545_measurements.base_pitch_w.unsqueeze(-1)
    # print("base_pitch_gac:", result)
    return result


def fill_ratio_aoa(env) -> torch.Tensor:
    result = torch.cat((env.soil.get_fill_ratio(), env.m545_measurements.bucket_aoa.unsqueeze(-1)), dim=-1)
    # print("fill_ratio_aoa:", result)
    # print("aoa:", env.m545_measurements.bucket_aoa)
    return result


def fill_ratio(env) -> torch.Tensor:
    result = env.soil.get_fill_ratio()
    # print("fill_ratio:", result)
    return result


def aoa(env) -> torch.Tensor:
    result = env.m545_measurements.bucket_aoa.unsqueeze(-1)
    # print("aoa:", result)
    return result


def soil_height(env) -> torch.Tensor:
    # Transform soil height from world to base frame
    world_heights = env.soil_height_futures
    # print("world_heights shape:", world_heights.shape)  # Print the shape of world_heights
    # Ensure the dimensions match by expanding root_pos_w if necessary
    root_pos_w_expanded = env.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, world_heights.size(1))
    result = world_heights - root_pos_w_expanded
    # print("soil_height:", result)
    return result


def max_depth(env) -> torch.Tensor:
    # Transform max depth from world to base frame
    world_depths = env.max_depth_futures
    # print("world_depths shape:", world_depths.shape)  # Print the shape of world_depths
    # Ensure the dimensions match by expanding root_pos_w if necessary
    root_pos_w_expanded = env.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, world_depths.size(1))
    result = world_depths - root_pos_w_expanded
    # print("max_depth:", result)
    return result


# def bucket_depth(env) -> torch.Tensor:
#     result = env.soil.get_bucket_depth()
#     # print("bucket_depth:", result)
#     return result


def pitch_vel(env) -> torch.Tensor:
    result = env.m545_measurements.j_pitch_vel[:, [0, 2]]
    # print("pitch_vel:", result)
    return result


def pullup_dist(env) -> torch.Tensor:
    result = env.pullup_dist.unsqueeze(-1)
    # print("pullup_dist:", result)
    return result


def soil_parameters(env) -> torch.Tensor:
    res_non_scaled = env.soil.get_soil_params()[:, 0 : env.soil.get_n_soil_params_to_sample()]
    # print("soil_parameters:", res_non_scaled)
    return res_non_scaled


def grid_perception_10x10(env) -> torch.Tensor:
    """
    Enhanced grid perception providing 10x10 grid of soil heights centered on bucket.

    Returns:
        torch.Tensor: Soil heights in a 10x10 grid [n_envs, 100] relative to base frame
    """
    # Grid configuration
    grid_size = 10
    grid_spacing = 0.3  # 30cm spacing between grid points

    # Get bucket position in world frame
    bucket_pos_w = env.m545_measurements.bucket_pos_w  # [n_envs, 3]

    # Create grid centered on bucket position
    # Grid extends from -1.35m to +1.35m in both x and y directions from bucket
    half_extent = (grid_size - 1) * grid_spacing / 2  # 1.35m

    # Create coordinate arrays
    x_offsets = torch.linspace(-half_extent, half_extent, grid_size, device=env.device)
    y_offsets = torch.linspace(-half_extent, half_extent, grid_size, device=env.device)

    # Create meshgrid
    x_grid, y_grid = torch.meshgrid(x_offsets, y_offsets, indexing='ij')

    # Flatten grid for batch processing
    x_flat = x_grid.flatten()  # [100]
    y_flat = y_grid.flatten()  # [100]

    # Expand for all environments
    x_query = bucket_pos_w[:, 0:1] + x_flat.unsqueeze(0)  # [n_envs, 100]
    y_query = bucket_pos_w[:, 1:2] + y_flat.unsqueeze(0)  # [n_envs, 100]

    # Get soil heights at grid points
    if hasattr(env.soil, 'get_soil_height_at_pos'):
        # 3D soil model
        soil_heights_w = env.soil.get_soil_height_at_pos(x_query, y_query)
    else:
        # 1D soil model - use only x coordinates
        soil_heights_w = env.soil.get_soil_height_at_pos(x_query)

    # Transform to base frame (relative to robot base)
    root_pos_w_expanded = env.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, grid_size * grid_size)
    soil_heights_base = soil_heights_w - root_pos_w_expanded

    return soil_heights_base


def grid_perception_5x5(env) -> torch.Tensor:
    """
    Compact 5x5 grid perception for environments with limited observation space.

    Returns:
        torch.Tensor: Soil heights in a 5x5 grid [n_envs, 25] relative to base frame
    """
    # Grid configuration
    grid_size = 5
    grid_spacing = 0.4  # 40cm spacing for wider coverage with fewer points

    # Get bucket position in world frame
    bucket_pos_w = env.m545_measurements.bucket_pos_w  # [n_envs, 3]

    # Create grid centered on bucket position
    half_extent = (grid_size - 1) * grid_spacing / 2  # 0.8m

    # Create coordinate arrays
    x_offsets = torch.linspace(-half_extent, half_extent, grid_size, device=env.device)
    y_offsets = torch.linspace(-half_extent, half_extent, grid_size, device=env.device)

    # Create meshgrid
    x_grid, y_grid = torch.meshgrid(x_offsets, y_offsets, indexing='ij')

    # Flatten grid for batch processing
    x_flat = x_grid.flatten()  # [25]
    y_flat = y_grid.flatten()  # [25]

    # Expand for all environments
    x_query = bucket_pos_w[:, 0:1] + x_flat.unsqueeze(0)  # [n_envs, 25]
    y_query = bucket_pos_w[:, 1:2] + y_flat.unsqueeze(0)  # [n_envs, 25]

    # Get soil heights at grid points
    if hasattr(env.soil, 'get_soil_height_at_pos'):
        # 3D soil model
        soil_heights_w = env.soil.get_soil_height_at_pos(x_query, y_query)
    else:
        # 1D soil model - use only x coordinates
        soil_heights_w = env.soil.get_soil_height_at_pos(x_query)

    # Transform to base frame (relative to robot base)
    root_pos_w_expanded = env.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, grid_size * grid_size)
    soil_heights_base = soil_heights_w - root_pos_w_expanded

    return soil_heights_base


def bucket_angle_of_attack_obs(env) -> torch.Tensor:
    """
    Bucket angle of attack as both observation and potential termination condition.

    Returns:
        torch.Tensor: Bucket angle of attack [n_envs, 1]
    """
    return env.m545_measurements.bucket_aoa.unsqueeze(-1)

