import gymnasium as gym

from . import agents, env_cfg

##
# Register Gym environments.
##

env_name = "Isaac-m545-digging"
print(f"Registering {env_name} environment!")

gym.register(
    id=env_name,
    entry_point="moleworks_ext.tasks.excavation.excavation_env_3d:ExcavationEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": env_cfg.m545_excavation_grid_perception_cfg.M545ExcavationGridPerceptionCfg,
        "rsl_rl_cfg_entry_point": agents.rsl_rl_cfg.M545PPORunnerCfg,
    },
)
