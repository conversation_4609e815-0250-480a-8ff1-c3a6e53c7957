#!/usr/bin/env bash

# Simplified GPU Training Job Submission Script for SLURM
# Provides user-friendly interface for both single and multi-GPU training

set -e  # Exit on any error

# GPU Configuration Table - Based on actual Euler cluster specifications
declare -A GPU_CONFIGS
# Format: "gpu_type:gpus_per_node:cpus_per_node:memory_per_node_gb"
GPU_CONFIGS["rtx_2080_ti"]="8:36:384"
GPU_CONFIGS["rtx_2080_ti_large"]="8:128:512"
GPU_CONFIGS["rtx_3090"]="8:128:512"
GPU_CONFIGS["rtx_4090"]="8:128:512"
GPU_CONFIGS["titan_rtx"]="8:128:512"
GPU_CONFIGS["quadro_rtx_6000"]="8:128:512"
GPU_CONFIGS["v100"]="8:48:768"
GPU_CONFIGS["v100_small"]="8:40:512"
GPU_CONFIGS["a100-pcie-40gb"]="8:48:768"
GPU_CONFIGS["a100_80gb"]="10:48:1024"

# Default configuration
DEFAULT_NUM_GPUS=1
DEFAULT_GPU_TYPE="rtx_4090"
DEFAULT_TIME="23:00:00"
DEFAULT_TASK="Isaac-m545-digging"
DEFAULT_NUM_ENVS=8000
DEFAULT_MAX_ITERATIONS=1000

# Function to list available GPU types
list_gpu_types() {
    echo "Available GPU types:"
    for gpu_type in "${!GPU_CONFIGS[@]}"; do
        IFS=':' read -r gpus_per_node cpus_per_node mem_per_node <<< "${GPU_CONFIGS[$gpu_type]}"
        printf "  %-20s - %2d GPUs/node, %3d CPUs/node, %4d GB/node\n" \
            "$gpu_type" "$gpus_per_node" "$cpus_per_node" "$mem_per_node"
    done | sort
}

# Function to display usage
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Simplified GPU Training Job Submission Script

OPTIONS:
    -g, --gpus NUM_GPUS         Number of GPUs to use (default: $DEFAULT_NUM_GPUS)
    -t, --gpu-type GPU_TYPE     GPU type to request (default: $DEFAULT_GPU_TYPE)
    -T, --time TIME             Job time limit (default: $DEFAULT_TIME)
    --task TASK                 Training task name (default: $DEFAULT_TASK)
    --num-envs NUM_ENVS         Number of environments (default: $DEFAULT_NUM_ENVS)
    --max-iterations ITERS      Maximum training iterations (default: $DEFAULT_MAX_ITERATIONS)
    --list-gpu-types            List all available GPU types and their specifications
    -h, --help                  Show this help message

EXAMPLES:
    # Single GPU training (default)
    $0

    # Train with 2 A100 GPUs
    $0 --gpus 2 --gpu-type a100_80gb

    # Train with 4 RTX 3090 GPUs with custom task
    $0 --gpus 4 --gpu-type rtx_3090 --task Isaac-m545-single-boulder

    # Train with 8 GPUs and more environments
    $0 --gpus 8 --num-envs 128000 --max-iterations 2000

NOTES:
    - Resources are automatically calculated based on cluster node configurations
    - For single GPU: Uses standard train.py script
    - For multi-GPU: Uses torch.distributed.run with train.py --distributed
    - Each GPU will handle NUM_ENVS/NUM_GPUS environments
    - For optimal performance, ensure NUM_ENVS is divisible by NUM_GPUS
EOF
}

# Parse command line arguments
NUM_GPUS=$DEFAULT_NUM_GPUS
GPU_TYPE=$DEFAULT_GPU_TYPE
TIME=$DEFAULT_TIME
TASK=$DEFAULT_TASK
NUM_ENVS=$DEFAULT_NUM_ENVS
MAX_ITERATIONS=$DEFAULT_MAX_ITERATIONS

while [[ $# -gt 0 ]]; do
    case $1 in
        -g|--gpus)
            NUM_GPUS="$2"
            shift 2
            ;;
        -t|--gpu-type)
            GPU_TYPE="$2"
            shift 2
            ;;
        -T|--time)
            TIME="$2"
            shift 2
            ;;
        --task)
            TASK="$2"
            shift 2
            ;;
        --num-envs)
            NUM_ENVS="$2"
            shift 2
            ;;
        --max-iterations)
            MAX_ITERATIONS="$2"
            shift 2
            ;;
        --list-gpu-types)
            list_gpu_types
            exit 0
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate inputs
if ! [[ "$NUM_GPUS" =~ ^[0-9]+$ ]] || [ "$NUM_GPUS" -lt 1 ]; then
    echo "Error: NUM_GPUS must be a positive integer"
    exit 1
fi

if ! [[ "$NUM_ENVS" =~ ^[0-9]+$ ]] || [ "$NUM_ENVS" -lt 1 ]; then
    echo "Error: NUM_ENVS must be a positive integer"
    exit 1
fi

# Validate GPU type
if [[ -z "${GPU_CONFIGS[$GPU_TYPE]}" ]]; then
    echo "Error: Unknown GPU type '$GPU_TYPE'"
    echo ""
    list_gpu_types
    exit 1
fi

# Parse GPU configuration for validation
IFS=':' read -r GPUS_PER_NODE CPUS_PER_NODE MEM_PER_NODE <<< "${GPU_CONFIGS[$GPU_TYPE]}"

# Validate that requested GPUs don't exceed node capacity
if [ "$NUM_GPUS" -gt "$GPUS_PER_NODE" ]; then
    echo "Error: Requested $NUM_GPUS GPUs exceeds the maximum of $GPUS_PER_NODE GPUs per node for $GPU_TYPE"
    exit 1
fi

# Check if NUM_ENVS is divisible by NUM_GPUS for optimal distribution
if [ "$NUM_GPUS" -gt 1 ] && [ $((NUM_ENVS % NUM_GPUS)) -ne 0 ]; then
    echo "Warning: NUM_ENVS ($NUM_ENVS) is not divisible by NUM_GPUS ($NUM_GPUS)"
    echo "This may lead to uneven environment distribution across GPUs"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Display configuration summary
echo "=========================================="
echo "GPU Training Job Configuration"
echo "=========================================="
echo "Number of GPUs: $NUM_GPUS"
echo "GPU Type: $GPU_TYPE"
echo "Task: $TASK"
echo "Total Environments: $NUM_ENVS"
if [ "$NUM_GPUS" -gt 1 ]; then
    echo "Environments per GPU: $((NUM_ENVS / NUM_GPUS))"
fi
echo "Max Iterations: $MAX_ITERATIONS"
echo "Job Time Limit: $TIME"
echo "=========================================="

# Export environment variables for the submit script
export NUM_GPUS
export GPU_TYPE

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"

# Submit the job using the cluster interface
echo "Submitting job using GPU submission script..."
bash "$SCRIPT_DIR/cluster_interface.sh" job moleworks_ext \
    --headless \
    --task "$TASK" \
    --num_envs "$NUM_ENVS" \
    --max_iterations "$MAX_ITERATIONS"

echo "Job submitted successfully!"
echo "Monitor job status with: squeue -u \$USER"
echo "View job output with: tail -f slurm-<job_id>.out"
