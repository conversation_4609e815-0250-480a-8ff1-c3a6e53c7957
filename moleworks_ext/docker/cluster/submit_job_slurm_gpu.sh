#!/usr/bin/env bash

# GPU Training Job Submission Script for SLURM
# Unified script for both single and multi-GPU training

set -e  # Exit on any error

# Load required modules
module load eth_proxy

# GPU Configuration Table - Based on actual Euler cluster specifications
declare -A GPU_CONFIGS
# Format: "gpu_type:gpus_per_node:cpus_per_node:memory_per_node_gb"
GPU_CONFIGS["rtx_2080_ti"]="8:36:384"
GPU_CONFIGS["rtx_2080_ti_large"]="8:128:512"
GPU_CONFIGS["rtx_3090"]="8:128:512"
GPU_CONFIGS["rtx_4090"]="8:128:512"
GPU_CONFIGS["titan_rtx"]="8:128:512"
GPU_CONFIGS["quadro_rtx_6000"]="8:128:512"
GPU_CONFIGS["v100"]="8:48:768"
GPU_CONFIGS["v100_small"]="8:40:512"
GPU_CONFIGS["a100-pcie-40gb"]="8:48:768"
GPU_CONFIGS["a100_80gb"]="10:48:1024"

# Default configuration
NUM_GPUS=${NUM_GPUS:-1}
GPU_TYPE=${GPU_TYPE:-"rtx_4090"}

# Function to get GPU configuration
get_gpu_config() {
    local gpu_type="$1"
    if [[ -n "${GPU_CONFIGS[$gpu_type]}" ]]; then
        echo "${GPU_CONFIGS[$gpu_type]}"
    else
        echo ""
    fi
}

# Validate GPU type and get configuration
GPU_CONFIG=$(get_gpu_config "$GPU_TYPE")
if [[ -z "$GPU_CONFIG" ]]; then
    echo "Error: Unknown GPU type '$GPU_TYPE'"
    echo "Available GPU types: ${!GPU_CONFIGS[@]}"
    exit 1
fi

# Parse GPU configuration
IFS=':' read -r GPUS_PER_NODE CPUS_PER_NODE MEM_PER_NODE <<< "$GPU_CONFIG"

# Validate that requested GPUs don't exceed node capacity
if [ "$NUM_GPUS" -gt "$GPUS_PER_NODE" ]; then
    echo "Error: Requested $NUM_GPUS GPUs exceeds the maximum of $GPUS_PER_NODE GPUs per node for $GPU_TYPE"
    exit 1
fi

# Calculate resources based on actual node configuration
if [ "$NUM_GPUS" -eq "$GPUS_PER_NODE" ]; then
    # Using full node
    TOTAL_CPUS=$CPUS_PER_NODE
    TOTAL_MEM=$MEM_PER_NODE
else
    # Using partial node - scale resources proportionally
    CPUS_PER_GPU=$((CPUS_PER_NODE / GPUS_PER_NODE))
    MEM_PER_GPU=$((MEM_PER_NODE / GPUS_PER_NODE))
    TOTAL_CPUS=$((NUM_GPUS * CPUS_PER_GPU))
    TOTAL_MEM=$((NUM_GPUS * MEM_PER_GPU))
fi

# Calculate CPUs per task and memory per CPU for SLURM
CPUS_PER_TASK=$((TOTAL_CPUS / NUM_GPUS))
MEM_PER_CPU=$((TOTAL_MEM * 1024 / TOTAL_CPUS))  # Convert GB to MB and divide by total CPUs

echo "=========================================="
echo "GPU Training Configuration"
echo "=========================================="
echo "Number of GPUs: $NUM_GPUS"
echo "GPU Type: $GPU_TYPE"
echo "Node Configuration: $GPUS_PER_NODE GPUs, $CPUS_PER_NODE CPUs, ${MEM_PER_NODE}GB per node"
if [ "$NUM_GPUS" -eq "$GPUS_PER_NODE" ]; then
    echo "Resource Usage: Full node"
else
    echo "Resource Usage: Partial node ($NUM_GPUS/$GPUS_PER_NODE GPUs)"
fi
echo "Total CPUs: $TOTAL_CPUS"
echo "Total Memory: ${TOTAL_MEM}GB"
echo "CPUs per task: $CPUS_PER_TASK"
echo "Memory per CPU: ${MEM_PER_CPU}MB"
echo "=========================================="

# Determine training configuration
if [ "$NUM_GPUS" -eq 1 ]; then
    echo "Training Mode: Single GPU"
    TRAINING_MODE="single"
else
    echo "Training Mode: Multi-GPU with ${NUM_GPUS} GPUs"
    TRAINING_MODE="multi"
fi

# Create job script with compute demands
cat <<EOT > job.sh
#!/bin/bash

#SBATCH --nodes=1
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=${TOTAL_CPUS}
#SBATCH --gpus-per-node=${GPU_TYPE}:${NUM_GPUS}
#SBATCH --time=23:00:00
#SBATCH --mem-per-cpu=${MEM_PER_CPU}M
#SBATCH --mail-type=END
#SBATCH --mail-user=name@mail
#SBATCH --job-name="training-${NUM_GPUS}gpu-$(date +"%Y-%m-%dT%H:%M")"

# Export environment variables for the job
export NUM_GPUS=${NUM_GPUS}
export GPU_TYPE=${GPU_TYPE}
export TOTAL_CPUS=${TOTAL_CPUS}
export TOTAL_MEM=${TOTAL_MEM}

# Set environment variables for multi-GPU training
export MASTER_ADDR=\$(hostname)
export MASTER_PORT=29500
export WORLD_SIZE=${NUM_GPUS}

# Set GPU optimization environment variables
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:1024,expandable_segments:True
export CUDA_LAUNCH_BLOCKING=0
export TORCH_CUDA_ARCH_LIST="8.0"
export NCCL_DEBUG=INFO

# Variables passed from submit script
dir="$1"
profile="$2"
# Skip empty mount args and "--" delimiter
shift 4
script_args="\$@"

# Set training mode for the container
export TRAINING_MODE="${TRAINING_MODE}"

# For single GPU training
if [ "${TRAINING_MODE}" = "single" ]; then
    export CUDA_VISIBLE_DEVICES=0
    export LOCAL_RANK=0
    export RANK=0
    echo "Starting single GPU training"
    # Override CLUSTER_PYTHON_EXECUTABLE for single GPU
    export CLUSTER_PYTHON_EXECUTABLE="scripts/rl/rsl_rl/train.py"
    bash "\$dir/docker/cluster/run_singularity.sh" "\$dir" "\$profile" "\$dir/docker/cluster/.env.cluster" "\$dir/docker/.env.moleworks_ext" -- \$script_args
else
    echo "Starting multi-GPU training with ${NUM_GPUS} GPUs"
    # Override CLUSTER_PYTHON_EXECUTABLE for multi-GPU distributed training
    export CLUSTER_PYTHON_EXECUTABLE="-m torch.distributed.run --nproc_per_node=${NUM_GPUS} scripts/rl/rsl_rl/train.py --distributed"
    bash "\$dir/docker/cluster/run_singularity.sh" "\$dir" "\$profile" "\$dir/docker/cluster/.env.cluster" "\$dir/docker/.env.moleworks_ext" -- \$script_args
fi
EOT

echo "Submitting job to SLURM..."
sbatch < job.sh
rm job.sh
echo "Job submitted successfully!"